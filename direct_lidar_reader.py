#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import serial
import struct
import math
import time

class DirectLidarReader:
    def __init__(self):
        # 雷达连接参数（从config文件中获取）
        self.port = 'COM3'
        self.baudrate = 230400
        
        # 筛选参数
        self.startAngle = 0
        self.endAngle = 16
        self.startDistance = 900
        self.endDistance = 1300
        
        # 串口连接
        self.ser = None
        
        print(f"🎯 筛选范围: {self.startAngle}-{self.endAngle}度, {self.startDistance}-{self.endDistance}mm")

    def connect(self):
        """连接雷达"""
        try:
            self.ser = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=1.0
            )
            print(f"✅ 成功连接雷达: {self.port}@{self.baudrate}")
            return True
        except Exception as e:
            print(f"❌ 连接雷达失败: {e}")
            return False

    def parse_lidar_packet(self, data):
        """解析雷达数据包"""
        try:
            if len(data) < 10:
                return []
            
            # 查找数据包头 (通常是特定的字节序列)
            # 这里需要根据具体的雷达协议来解析
            # 暂时使用简单的解析方法
            
            points = []
            
            # 假设数据包格式：每个点包含角度(2字节) + 距离(2字节) + 强度(1字节)
            point_size = 5
            num_points = len(data) // point_size
            
            for i in range(num_points):
                offset = i * point_size
                if offset + point_size > len(data):
                    break
                
                try:
                    # 解析角度（假设是0.01度单位）
                    angle_raw = struct.unpack('<H', data[offset:offset+2])[0]
                    angle_deg = angle_raw * 0.01
                    
                    # 解析距离（假设是mm单位）
                    distance_raw = struct.unpack('<H', data[offset+2:offset+4])[0]
                    distance_mm = distance_raw
                    
                    # 解析强度
                    confidence = struct.unpack('B', data[offset+4:offset+5])[0]
                    
                    # 筛选目标范围
                    if (self.startAngle <= angle_deg <= self.endAngle and 
                        self.startDistance <= distance_mm <= self.endDistance and
                        distance_mm > 0):  # 过滤无效距离
                        
                        points.append({
                            'angle_deg': angle_deg,
                            'distance_mm': distance_mm,
                            'confidence': confidence
                        })
                        
                except struct.error:
                    continue
            
            return points
            
        except Exception as e:
            print(f"❌ 解析数据包失败: {e}")
            return []

    def read_data(self):
        """读取雷达数据"""
        if not self.ser:
            print("❌ 雷达未连接")
            return []
        
        try:
            # 读取数据
            if self.ser.in_waiting > 0:
                data = self.ser.read(self.ser.in_waiting)
                return self.parse_lidar_packet(data)
            return []
            
        except Exception as e:
            print(f"❌ 读取数据失败: {e}")
            return []

    def run(self):
        """运行雷达数据读取"""
        if not self.connect():
            return
        
        print(f"🚀 开始读取雷达数据...")
        frame_count = 0
        
        try:
            while True:
                points = self.read_data()
                
                if points:
                    frame_count += 1
                    print(f"\n🎯 第{frame_count}帧: 找到 {len(points)} 个目标点")
                    
                    # 显示前5个点
                    for i, point in enumerate(points[:5]):
                        print(f"  点{i+1}: {point['angle_deg']:.2f}度, {point['distance_mm']:.0f}mm, 强度{point['confidence']}")
                    
                    if len(points) > 5:
                        print(f"  ... 还有 {len(points)-5} 个点")
                
                time.sleep(0.1)  # 100ms间隔
                
        except KeyboardInterrupt:
            print("\n程序退出")
        finally:
            if self.ser:
                self.ser.close()

if __name__ == "__main__":
    reader = DirectLidarReader()
    reader.run()
