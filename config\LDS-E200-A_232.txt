#connect
type:uart
#windows  com name
connectArg:com3
#linux com name
#connectArg:/dev/ttyUSB3
#baud rate 230400 460800 921600
connectArg2:230400 
#data
output_360:1
from_zero:0
is_group_listener:0
group_ip:**********
service_port:8888
is_open_service:1
error_circle:3
error_scale:0.9
#get
model:1
version:1
#set
unit_is_mm:-1
with_confidence:-1
with_smooth:1
with_deshadow:1
resample_res:1
rpm:-1

# 添加阴影滤波器配置，允许0-75度范围的数据通过
shadow_filter.enable:1
shadow_filter.min_angle:0
shadow_filter.max_angle:75
shadow_filter.window:5
shadow_filter.neighbors:10
median_filter.enable:1
median_filter.window:1

