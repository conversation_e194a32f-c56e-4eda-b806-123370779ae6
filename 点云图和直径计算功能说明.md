# 雷达点云图和直径计算功能说明

## 新增功能概述

在 `test_lidar_simple.py` 中新增了两个重要功能：
1. **实时点云图显示** - 极坐标雷达图可视化
2. **直径计算功能** - 自动计算角度范围内最近距离

---

## 功能1：实时点云图显示

### 🎯 **功能特点**
- **极坐标显示**：使用雷达图形式显示点云数据
- **实时更新**：每帧数据自动更新图形
- **角度范围标识**：红色虚线标识检测角度范围
- **最近点高亮**：红色圆点标识最近的检测点
- **颜色编码**：根据信号强度显示不同颜色

### 📊 **图形元素**
```
🎯 雷达点云图组成：
├── 极坐标网格 (0-2000mm, 0-16度)
├── 散点图 (角度 vs 距离)
├── 颜色条 (信号强度 0-100)
├── 角度范围线 (红色虚线)
└── 最近点标记 (红色圆点)
```

### ⚙️ **配置参数**
```python
# 图形设置
- 距离范围: 0-2000mm
- 角度范围: 0-16度 (可配置)
- 更新频率: 实时 (每帧)
- 颜色映射: viridis (蓝-绿-黄)
```

---

## 功能2：直径计算功能

### 🎯 **功能特点**
- **最近距离检测**：自动找到角度范围内最近的点
- **历史平均**：基于最近10帧计算平均最近距离
- **角度信息**：显示最近点的角度位置
- **信号质量**：显示最近点的信号强度

### 📏 **计算逻辑**
```python
计算流程：
1. 筛选角度范围内的有效点
2. 找到距离最小的点
3. 记录到历史缓存 (最多10帧)
4. 计算最近5帧的平均值
5. 输出当前值和平均值
```

### 📊 **输出信息**
```
📏 最近距离: 1123mm (角度0.2°, 强度73)
📈 平均最近距离: 1125mm (基于最近5帧)
```

---

## 使用方法

### 🚀 **启动程序**
```bash
# 1. 先启动C++雷达程序
.\build\Debug\demo.exe config\LDS-E200-A_232.txt

# 2. 再启动Python程序 (会自动显示点云图)
python test_lidar_simple.py
```

### 🎮 **交互操作**
- **查看点云图**：程序启动后自动弹出图形窗口
- **实时更新**：图形会随雷达数据实时更新
- **关闭程序**：Ctrl+C 退出，图形窗口自动关闭

---

## 输出示例

### 📺 **控制台输出**
```
🎯 第10帧: 检测到 81 个目标
📊 范围: 0.0°-16.0°, 1123-1155mm, 强度72-73
📏 最近距离: 1123mm (角度2.4°, 强度73)
📈 平均最近距离: 1125mm (基于最近5帧)
📋 前5个目标:
  目标1: 0.0°, 1124mm, 强度73
  目标2: 0.2°, 1124mm, 强度73
  目标3: 0.4°, 1124mm, 强度73
  目标4: 0.6°, 1124mm, 强度73
  目标5: 0.8°, 1124mm, 强度73
  ... 还有 76 个目标
```

### 🎨 **点云图显示**
```
极坐标雷达图特征：
- 中心点：雷达位置 (0,0)
- 径向轴：距离 (0-2000mm)
- 角度轴：方位角 (0-16度)
- 散点：检测到的目标点
- 颜色：信号强度 (深蓝=弱, 黄色=强)
```

---

## 技术实现

### 🔧 **核心函数**

#### 1. 点云图初始化
```python
def init_plot(self):
    """初始化极坐标点云图"""
    - 创建极坐标子图
    - 设置角度和距离范围
    - 配置颜色映射和网格
```

#### 2. 点云图更新
```python
def update_plot(self, points):
    """实时更新点云图"""
    - 转换角度为弧度
    - 清除旧散点，绘制新散点
    - 标识角度范围和最近点
```

#### 3. 直径计算
```python
def calculate_diameter(self, points):
    """计算最近距离"""
    - 筛选角度范围内的点
    - 找到最小距离点
    - 维护历史记录和平均值
```

### 📦 **依赖库**
```python
import matplotlib.pyplot as plt  # 图形绘制
import numpy as np              # 数值计算
import threading               # 线程安全
import time                   # 时间戳
```

---

## 配置选项

### ⚙️ **可调参数**
```python
# 在 __init__ 函数中修改
self.config = {
    'ScanPointCloudZone': {
        'startAngle': 0,      # 起始角度
        'endAngle': 16,       # 结束角度
        'startDistance': 900, # 最小距离 (未使用)
        'endDistance': 1300,  # 最大距离 (未使用)
    }
}

# 点云图设置
self.enable_plot = True          # 是否启用点云图
self.max_history_size = 10       # 历史记录大小
```

### 🎨 **图形自定义**
```python
# 在 init_plot() 中修改
- 图形大小: figsize=(10, 8)
- 距离范围: set_ylim(0, 2000)
- 颜色映射: cmap='viridis'
- 点大小: s=30
- 透明度: alpha=0.8
```

---

## 故障排除

### ❌ **常见问题**

1. **图形不显示**
   - 检查是否安装了 matplotlib
   - 确认 `enable_plot = True`

2. **图形卡顿**
   - 减少显示的点数量
   - 增加更新间隔

3. **最近距离异常**
   - 检查角度范围设置
   - 确认雷达数据有效性

### ✅ **性能优化**
- 只显示前5个目标点（避免输出过多）
- 使用线程锁保证图形更新安全
- 历史记录限制大小避免内存泄漏

---

## 扩展功能建议

### 🚀 **可能的改进**
1. **保存点云数据**：导出为CSV或JSON格式
2. **多目标跟踪**：跟踪特定目标的运动轨迹
3. **区域检测**：设置多个检测区域
4. **报警功能**：距离过近时发出警告
5. **数据录制**：录制和回放雷达数据

### 📊 **统计分析**
1. **距离分布图**：显示距离的统计分布
2. **角度热力图**：显示不同角度的检测频率
3. **信号质量分析**：分析信号强度变化趋势
