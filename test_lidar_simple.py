#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import socket
import struct
import math
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.animation import FuncAnimation
import threading
import time

class SimpleLidarReceiver:
    def __init__(self):
        # UDP设置
        self.server_ip = '127.0.0.1'
        self.server_port = 3008

        print(f"🚀 初始化UDP接收器...")
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.sock.bind((self.server_ip, self.server_port))
           

            # 设置超时，避免无限等待
            self.sock.settimeout(5.0)
            
        except Exception as e:
            
            raise
        
        # 使用默认配置
        self.config = {
            'ScanPointCloudZone': {
                'startAngle': 0,
                'endAngle': 8,  # 扩大角度范围用于可视化
                'startDistance': 900,
                'endDistance': 1300,
                'CompensationAngle': 0
            }
        }

        self.startAngle = self.config['ScanPointCloudZone']['startAngle']
        self.endAngle = self.config['ScanPointCloudZone']['endAngle']
        self.startDistance = self.config['ScanPointCloudZone']['startDistance']
        self.endDistance = self.config['ScanPointCloudZone']['endDistance']

        # 点云图相关
        self.latest_points = []
        self.plot_lock = threading.Lock()
        self.fig = None
        self.ax = None
        self.scatter = None
        self.enable_plot = True

        # 直径计算相关
        self.min_distance_history = []
        self.diameter_history = []  # 专门存储直径历史数据
        self.max_history_size = 30

        # 地面参考距离设置
        self.ground_distance_d1 = 443  # mm，相机离地距离（根据实际测量调整）
        self.ground_tolerance = 10     # mm，地面检测容差范围
        self.lidar_radius = 21        # mm，雷达本身半径

        # 初始化点云图
        if self.enable_plot:
            self.init_plot()

    def init_plot(self):
        """初始化点云图"""
        plt.ion()  # 开启交互模式
        self.fig, self.ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))

        # 设置极坐标图
        self.ax.set_title('LiDAR Point Cloud (Real-time)', fontsize=14, pad=20)
        self.ax.set_theta_zero_location('N')  # 0度在上方
        self.ax.set_theta_direction(-1)  # 顺时针

        # 设置角度范围 (转换为弧度)
        theta_min = np.radians(self.startAngle)
        theta_max = np.radians(self.endAngle)
        self.ax.set_thetamin(np.degrees(theta_min))
        self.ax.set_thetamax(np.degrees(theta_max))

        # 设置距离范围
        self.ax.set_ylim(0, 2000)  # 0-2米
        self.ax.set_ylabel('Distance (mm)', labelpad=30)

        # 初始化散点图（不使用颜色映射）
        self.scatter = self.ax.scatter([], [], c='blue', s=20, alpha=0.7)

        # 添加网格
        self.ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show(block=False)

    def update_plot(self, points, diameter_info=None):
        """更新点云图，包含直径信息显示"""
        if not self.enable_plot or not points:
            return

        with self.plot_lock:
            try:
                # 转换数据格式
                angles = [np.radians(p['angle_deg']) for p in points]
                distances = [p['distance_mm'] for p in points]

                # 清除旧的散点
                self.ax.clear()

                # 计算平均直径用于标题显示
                avg_diameter_display = "N/A"
                if diameter_info and diameter_info.get('avg_diameter') is not None:
                    avg_diameter_display = f"{diameter_info['avg_diameter']:.1f}mm"

                # 重新设置图形属性，标题包含直径信息
                title = f'LiDAR Point Cloud - Diameter: {avg_diameter_display}'
                self.ax.set_title(title, fontsize=14, pad=20)
                self.ax.set_theta_zero_location('N')
                self.ax.set_theta_direction(-1)
                self.ax.set_thetamin(self.startAngle)
                self.ax.set_thetamax(self.endAngle)
                self.ax.set_ylim(0, 2000)
                self.ax.set_ylabel('Distance (mm)', labelpad=30)
                self.ax.grid(True, alpha=0.3)

                # 绘制新的散点（使用固定颜色）
                self.ax.scatter(angles, distances, c='blue', s=30, alpha=0.8)

                # 添加角度范围标识线
                theta_min = np.radians(self.startAngle)
                theta_max = np.radians(self.endAngle)
                self.ax.plot([theta_min, theta_min], [0, 2000], 'r--', alpha=0.5, linewidth=2, label='Detection Range')
                self.ax.plot([theta_max, theta_max], [0, 2000], 'r--', alpha=0.5, linewidth=2)

                # 标注最近点
                if distances:
                    min_dist = min(distances)
                    min_idx = distances.index(min_dist)
                    min_angle = angles[min_idx]
                    self.ax.plot(min_angle, min_dist, 'ro', markersize=8, label=f'Closest: {min_dist:.0f}mm')

                # 添加地面参考线
                ground_radius = self.ground_distance_d1
                theta_range = np.linspace(theta_min, theta_max, 50)
                ground_line = [ground_radius] * len(theta_range)
                self.ax.plot(theta_range, ground_line, 'g--', alpha=0.6, linewidth=2, label=f'Ground Ref: {ground_radius}mm')

                # 添加直径信息文本框
                if diameter_info and diameter_info.get('avg_diameter') is not None:
                    info_text = f"Diameter: {diameter_info['avg_diameter']:.1f}mm\n"
                    
                    info_text += f"Samples: {len(self.diameter_history)}/30\n"
                    info_text += f"d2: {diameter_info['d2_measured']:.0f}mm"

                    # 在图的右上角添加文本框
                    self.ax.text(0.02, 0.98, info_text, transform=self.ax.transAxes,
                                fontsize=10, verticalalignment='top',
                                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

                self.ax.legend(loc='upper right', bbox_to_anchor=(1.0, 0.8))

                # 刷新图形
                plt.draw()
                plt.pause(0.01)

            except Exception as e:
                print(f"❌ 更新点云图失败: {e}")

    def calculate_diameter(self, points):
        """基于地面参考的直径计算 - d2取所有点的最小值"""
        if not points:
            return None

        # 筛选角度范围内的点
        valid_points = [p for p in points if self.startAngle <= p['angle_deg'] <= self.endAngle]

        if not valid_points:
            return None

        # 分离地面点和物体点（仅用于统计显示）
        ground_points = []
        object_points = []

        ground_min = self.ground_distance_d1 - self.ground_tolerance
        ground_max = self.ground_distance_d1 + self.ground_tolerance

        for point in valid_points:
            distance = point['distance_mm']
            if ground_min <= distance <= ground_max:
                ground_points.append(point)
            else:
                object_points.append(point)

        # d2取所有有效点中的最小距离（不区分地面点和物体点）
        d2_measured = min(p['distance_mm'] for p in valid_points)
        min_point = next(p for p in valid_points if p['distance_mm'] == d2_measured)

        # 计算直径：d = d1 - d2（d2已经减去了雷达半径）
        diameter = self.ground_distance_d1 - d2_measured

        # 添加到历史记录
        self.min_distance_history.append({
            'distance': d2_measured,
            'diameter': diameter,
            'angle': min_point['angle_deg'],
            'confidence': min_point['confidence'],
            'timestamp': time.time()
        })

        # 专门记录直径历史（用于稳定显示）
        self.diameter_history.append(diameter)

        # 保持历史记录大小
        if len(self.min_distance_history) > self.max_history_size:
            self.min_distance_history.pop(0)
        if len(self.diameter_history) > self.max_history_size:
            self.diameter_history.pop(0)

        # 计算平均直径（基于最近10次测量）
        avg_diameter = sum(self.diameter_history) / len(self.diameter_history) if self.diameter_history else None

        return {
            'diameter': diameter,
            'avg_diameter': avg_diameter,
            'ground_detected': len(ground_points) > 0,
            'ground_points': len(ground_points),
            'object_points': len(object_points),
            'min_distance': d2_measured,
            'min_angle': min_point['angle_deg'],
            'min_confidence': min_point['confidence'],
            'd1_reference': self.ground_distance_d1,
            'd2_measured': d2_measured,
            'total_points': len(valid_points)
        }

    def parse_userdata(self, data):
        """解析简化的数据包格式"""
        try:
            # 简化包格式：type(4) + idx(4) + device(16) + baudrate(4) + point_count(4) + 点数据
            if len(data) < 32:
                return None

            # 添加调试信息
            print(f"🔍 接收到数据包，长度: {len(data)}字节")
            print(f"🔍 前32字节十六进制: {data[:32].hex()}")

            offset = 0
            data_type = struct.unpack('I', data[offset:offset+4])[0]
            offset += 4
            idx = struct.unpack('I', data[offset:offset+4])[0]
            offset += 4
            device = data[offset:offset+16].decode('utf-8', errors='ignore').rstrip('\x00')
            offset += 16
            baudrate = struct.unpack('I', data[offset:offset+4])[0]
            offset += 4
            point_count = struct.unpack('I', data[offset:offset+4])[0]
            offset += 4

            print(f" 数据包: type={data_type}, idx={idx}, device={device}@{baudrate}, 点数={point_count}")
            print(f" 点数据开始偏移: {offset}, 剩余数据: {len(data)-offset}字节")

            # 显示前几个点的原始数据
            if len(data) > offset + 36:  # 至少4个点的数据
                print(f" 前36字节点数据十六进制: {data[offset:offset+36].hex()}")

            # 解析点数据
            if data_type == 1 and point_count > 0:
                return self.parse_points(data, offset, point_count)

            return None

        except Exception as e:
            print(f"❌ 解析失败: {e}")
            return None

    def parse_points(self, data, offset, point_count):
        """解析点数据数组"""
        try:
            # C++结构体对齐：float(4) + float(4) + unsigned char(1) + padding(3) = 12字节
            point_size = 12  # 考虑结构体对齐
            expected_size = point_count * point_size

            if offset + expected_size > len(data):
                print(f" 数据长度不足: 需要{expected_size}字节, 实际{len(data)-offset}字节")
                return []

            valid_points = []

            # 只解析前10个点用于调试
            debug_count = min(10, point_count)
            print(f"🔍 调试前{debug_count}个点:")

            for i in range(debug_count):
                point_offset = offset + i * point_size

                try:
                    # 显示原始字节（12字节结构体）
                    raw_bytes = data[point_offset:point_offset+12]
                    print(f"  点{i+1} 原始数据: {raw_bytes.hex()}")

                    angle = struct.unpack('f', data[point_offset:point_offset+4])[0]
                    distance = struct.unpack('f', data[point_offset+4:point_offset+8])[0]
                    confidence = struct.unpack('B', data[point_offset+8:point_offset+9])[0]
                    # 跳过3字节填充：point_offset+9到point_offset+12

                    print(f"  点{i+1} 解析: 角度={angle:.6f}rad({angle*180/math.pi:.2f}°), 距离={distance:.6f}m({distance*1000:.0f}mm), 强度={confidence}")

                    # 验证数据合理性 - 放宽条件用于调试
                    if (not math.isnan(angle) and not math.isinf(angle) and
                        not math.isnan(distance) and not math.isinf(distance)):

                        angle_deg = angle * 180.0 / math.pi
                        distance_mm = distance * 1000.0 - self.lidar_radius*2  # 减去雷达半径得到实际距离

                        # 过滤条件：角度范围内 + 距离>0（过滤无效距离）
                        if (self.startAngle <= angle_deg <= self.endAngle and
                            distance_mm > 0):
                            valid_points.append({
                                'angle_deg': angle_deg,
                                'distance_mm': distance_mm,
                                'confidence': confidence
                            })

                except struct.error as e:
                    print(f"  点{i+1} 解析错误: {e}")
                    continue

            # 继续解析剩余的点（不打印调试信息）
            for i in range(debug_count, point_count):
                point_offset = offset + i * point_size

                try:
                    angle = struct.unpack('f', data[point_offset:point_offset+4])[0]
                    distance = struct.unpack('f', data[point_offset+4:point_offset+8])[0]
                    confidence = struct.unpack('B', data[point_offset+8:point_offset+9])[0]

                    if (not math.isnan(angle) and not math.isinf(angle) and
                        not math.isnan(distance) and not math.isinf(distance)):

                        angle_deg = angle * 180.0 / math.pi
                        distance_mm = distance * 1000.0 - self.lidar_radius



                        # 过滤条件：角度范围内 + 距离>0（过滤无效距离）
                        if (self.startAngle <= angle_deg <= self.endAngle and
                            distance_mm > 0):
                            valid_points.append({
                                'angle_deg': angle_deg,
                                'distance_mm': distance_mm,
                                'confidence': confidence
                            })

                except struct.error:
                    continue

            return valid_points

        except Exception as e:
            print(f"❌ 解析点数据失败: {e}")
            return []



    def run(self):
        """运行接收器"""
        print(f" 简化雷达数据接收器启动")
        print(f" 监听 {self.server_ip}:{self.server_port}")
        
        frame_count = 0
        
        try:
            while True:
                try:
                    print(" 等待UDP数据...")
                    data, addr = self.sock.recvfrom(65536)
                    frame_count += 1
                    print(f" 接收到来自 {addr} 的数据包 #{frame_count}")

                    valid_points = self.parse_userdata(data)
                except socket.timeout:
                    print(" 接收超时，继续等待...")
                    continue
                except Exception as e:
                    print(f"❌ 接收数据错误: {e}")
                    continue
                
                if valid_points:
                    print(f"\n 第{frame_count}帧: 检测到 {len(valid_points)} 个目标")

                    # 分析数据特征
                    distances = [p['distance_mm'] for p in valid_points]
                    angles = [p['angle_deg'] for p in valid_points]
                    confidences = [p['confidence'] for p in valid_points]

                    print(f" 范围: {min(angles):.1f}°-{max(angles):.1f}°, {min(distances):.0f}-{max(distances):.0f}mm, 强度{min(confidences)}-{max(confidences)}")

                    # 打印所有有效距离
                    distances_sorted = sorted(distances)
                    print(f" 所有距离: {', '.join([f'{d:.0f}mm' for d in distances_sorted])}")

                    # 基于地面参考的直径计算
                    diameter_info = self.calculate_diameter(valid_points)
                    if diameter_info:
                        print(f" 地面参考: d1={diameter_info['d1_reference']}mm (±{self.ground_tolerance}mm)")
                        print(f" 地面检测: {'✅检测到' if diameter_info['ground_detected'] else '❌未检测到'} ({diameter_info['ground_points']}个地面点)")

                        if diameter_info['diameter'] is not None:
                            print(f"📏 最小距离: d2={diameter_info['d2_measured']:.0f}mm (角度{diameter_info['min_angle']:.1f}°, 强度{diameter_info['min_confidence']})")
                            print(f"📐 直径计算: d = d1 - d2 = {diameter_info['d1_reference']} - {diameter_info['d2_measured']:.0f} = {diameter_info['diameter']:.0f}mm (d2已减去雷达半径{self.lidar_radius}mm)")
                            print(f"🎯 测量直径: {diameter_info['diameter']:.0f}mm")

                            if diameter_info['avg_diameter'] is not None:
                                print(f"� 平均直径: {diameter_info['avg_diameter']:.0f}mm (基于最近{len([h for h in self.min_distance_history if h.get('diameter') is not None])}帧)")
                        else:
                            print(f" 未检测到物体: 只有{diameter_info['ground_points']}个地面点")

                        print(f" 点分布: 地面{diameter_info['ground_points']}个, 物体{diameter_info['object_points']}个, 总计{diameter_info['total_points']}个")

                    # 更新点云图（传递直径信息）
                    if self.enable_plot:
                        self.update_plot(valid_points, diameter_info)

                    # 只显示前5个检测点（避免输出过多）
                    print(f"📋 前5个目标:")
                    for i, point in enumerate(valid_points[:5]):
                        print(f"  目标{i+1}: {point['angle_deg']:.1f}°, {point['distance_mm']:.0f}mm, 强度{point['confidence']}")

                    if len(valid_points) > 5:
                        print(f"  ... 还有 {len(valid_points)-5} 个目标")

                else:
                    if frame_count % 10 == 1:
                        print(f"⚠️ 第{frame_count}帧: 未检测到目标")

        except KeyboardInterrupt:
            print("\n程序退出")
        finally:
            self.sock.close()
            if self.enable_plot and plt.get_fignums():
                plt.close('all')

if __name__ == "__main__":
    receiver = SimpleLidarReceiver()
    receiver.run()
