
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:25 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:25 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "D:\\opennier\\OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)\\Win64-Release\\sdk\\libs"
        - "D:\\python3.10\\Scripts\\"
        - "D:\\python3.10\\"
        - "D:\\orbbec\\OrbbecSDK 2.4.3\\bin"
        - "D:\\openni22\\0013e-main\\OpenNI_2.3.0.66\\Windows\\SDK\\x64\\Redist"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\DTS\\Binn\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\pycharm\\PyCharm 2025.1.2\\bin"
        - "D:\\cursor\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:\\vscode\\Microsoft VS Code\\bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:25 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
      鐢熸垚鍚姩鏃堕棿涓?2025/8/5 14:19:22銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\4.1.0-rc3\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdC.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\4.1.0-rc3\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淒:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\4.1.0-rc3\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.89
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/workfile/lanhai-driver-branch_noweb/build/CMakeFiles/4.1.0-rc3/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:103 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:25 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/"
      - "D:/python3.10/Scripts/"
      - "D:/python3.10/"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/pycharm/PyCharm 2025.1.2/bin/"
      - "D:/cursor/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/vscode/Microsoft VS Code/bin/"
    searched_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.com"
    found: "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "D:\\opennier\\OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)\\Win64-Release\\sdk\\libs"
        - "D:\\python3.10\\Scripts\\"
        - "D:\\python3.10\\"
        - "D:\\orbbec\\OrbbecSDK 2.4.3\\bin"
        - "D:\\openni22\\0013e-main\\OpenNI_2.3.0.66\\Windows\\SDK\\x64\\Redist"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\DTS\\Binn\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\pycharm\\PyCharm 2025.1.2\\bin"
        - "D:\\cursor\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:\\vscode\\Microsoft VS Code\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:25 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/"
      - "D:/python3.10/Scripts/"
      - "D:/python3.10/"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/pycharm/PyCharm 2025.1.2/bin/"
      - "D:/cursor/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/vscode/Microsoft VS Code/bin/"
    searched_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/lld-link.com"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/lld-link.exe"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/lld-link"
      - "D:/python3.10/Scripts/lld-link.com"
      - "D:/python3.10/Scripts/lld-link.exe"
      - "D:/python3.10/Scripts/lld-link"
      - "D:/python3.10/lld-link.com"
      - "D:/python3.10/lld-link.exe"
      - "D:/python3.10/lld-link"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/lld-link.com"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/lld-link.exe"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/lld-link"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/lld-link.com"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/lld-link.exe"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/lld-link.com"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/lld-link.exe"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/lld-link"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/lld-link"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "D:/pycharm/PyCharm 2025.1.2/bin/lld-link.com"
      - "D:/pycharm/PyCharm 2025.1.2/bin/lld-link.exe"
      - "D:/pycharm/PyCharm 2025.1.2/bin/lld-link"
      - "D:/cursor/cursor/resources/app/bin/lld-link.com"
      - "D:/cursor/cursor/resources/app/bin/lld-link.exe"
      - "D:/cursor/cursor/resources/app/bin/lld-link"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.com"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.exe"
      - "C:/Users/<USER>/.dotnet/tools/lld-link"
      - "D:/vscode/Microsoft VS Code/bin/lld-link.com"
      - "D:/vscode/Microsoft VS Code/bin/lld-link.exe"
      - "D:/vscode/Microsoft VS Code/bin/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "D:\\opennier\\OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)\\Win64-Release\\sdk\\libs"
        - "D:\\python3.10\\Scripts\\"
        - "D:\\python3.10\\"
        - "D:\\orbbec\\OrbbecSDK 2.4.3\\bin"
        - "D:\\openni22\\0013e-main\\OpenNI_2.3.0.66\\Windows\\SDK\\x64\\Redist"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\DTS\\Binn\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\pycharm\\PyCharm 2025.1.2\\bin"
        - "D:\\cursor\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:\\vscode\\Microsoft VS Code\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:25 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "link"
    candidate_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/"
      - "D:/python3.10/Scripts/"
      - "D:/python3.10/"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/pycharm/PyCharm 2025.1.2/bin/"
      - "D:/cursor/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/vscode/Microsoft VS Code/bin/"
    searched_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.com"
    found: "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe"
    search_context:
      ENV{PATH}:
        - "D:\\opennier\\OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)\\Win64-Release\\sdk\\libs"
        - "D:\\python3.10\\Scripts\\"
        - "D:\\python3.10\\"
        - "D:\\orbbec\\OrbbecSDK 2.4.3\\bin"
        - "D:\\openni22\\0013e-main\\OpenNI_2.3.0.66\\Windows\\SDK\\x64\\Redist"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\DTS\\Binn\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\pycharm\\PyCharm 2025.1.2\\bin"
        - "D:\\cursor\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:\\vscode\\Microsoft VS Code\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:25 (project)"
    mode: "program"
    variable: "CMAKE_MT"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mt"
    candidate_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/"
      - "D:/python3.10/Scripts/"
      - "D:/python3.10/"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/pycharm/PyCharm 2025.1.2/bin/"
      - "D:/cursor/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/vscode/Microsoft VS Code/bin/"
    searched_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt.com"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt.exe"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/mt"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/mt.com"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/mt.exe"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/mt"
      - "D:/python3.10/Scripts/mt.com"
      - "D:/python3.10/Scripts/mt.exe"
      - "D:/python3.10/Scripts/mt"
      - "D:/python3.10/mt.com"
      - "D:/python3.10/mt.exe"
      - "D:/python3.10/mt"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/mt.com"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/mt.exe"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/mt"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/mt.com"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/mt.exe"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/mt"
      - "C:/Windows/System32/mt.com"
      - "C:/Windows/System32/mt.exe"
      - "C:/Windows/System32/mt"
      - "C:/Windows/mt.com"
      - "C:/Windows/mt.exe"
      - "C:/Windows/mt"
      - "C:/Windows/System32/wbem/mt.com"
      - "C:/Windows/System32/wbem/mt.exe"
      - "C:/Windows/System32/wbem/mt"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/mt"
      - "C:/Windows/System32/OpenSSH/mt.com"
      - "C:/Windows/System32/OpenSSH/mt.exe"
      - "C:/Windows/System32/OpenSSH/mt"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mt.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mt.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/mt"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/mt.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/mt.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/mt"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/mt.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/mt.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/mt"
      - "C:/Program Files/dotnet/mt.com"
      - "C:/Program Files/dotnet/mt.exe"
      - "C:/Program Files/dotnet/mt"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/mt"
      - "C:/Program Files/CMake/bin/mt.com"
      - "C:/Program Files/CMake/bin/mt.exe"
      - "C:/Program Files/CMake/bin/mt"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/mt.com"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/mt.exe"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/mt"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/mt.com"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/mt.exe"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/mt"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/mt.com"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/mt.exe"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/mt"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/mt"
      - "D:/pycharm/PyCharm 2025.1.2/bin/mt.com"
      - "D:/pycharm/PyCharm 2025.1.2/bin/mt.exe"
      - "D:/pycharm/PyCharm 2025.1.2/bin/mt"
      - "D:/cursor/cursor/resources/app/bin/mt.com"
      - "D:/cursor/cursor/resources/app/bin/mt.exe"
      - "D:/cursor/cursor/resources/app/bin/mt"
      - "C:/Users/<USER>/.dotnet/tools/mt.com"
      - "C:/Users/<USER>/.dotnet/tools/mt.exe"
      - "C:/Users/<USER>/.dotnet/tools/mt"
      - "D:/vscode/Microsoft VS Code/bin/mt.com"
      - "D:/vscode/Microsoft VS Code/bin/mt.exe"
      - "D:/vscode/Microsoft VS Code/bin/mt"
    found: false
    search_context:
      ENV{PATH}:
        - "D:\\opennier\\OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)\\Win64-Release\\sdk\\libs"
        - "D:\\python3.10\\Scripts\\"
        - "D:\\python3.10\\"
        - "D:\\orbbec\\OrbbecSDK 2.4.3\\bin"
        - "D:\\openni22\\0013e-main\\OpenNI_2.3.0.66\\Windows\\SDK\\x64\\Redist"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\DTS\\Binn\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\pycharm\\PyCharm 2025.1.2\\bin"
        - "D:\\cursor\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:\\vscode\\Microsoft VS Code\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:25 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lib"
    candidate_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/"
      - "D:/python3.10/Scripts/"
      - "D:/python3.10/"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/pycharm/PyCharm 2025.1.2/bin/"
      - "D:/cursor/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/vscode/Microsoft VS Code/bin/"
    searched_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.com"
    found: "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.exe"
    search_context:
      ENV{PATH}:
        - "D:\\opennier\\OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)\\Win64-Release\\sdk\\libs"
        - "D:\\python3.10\\Scripts\\"
        - "D:\\python3.10\\"
        - "D:\\orbbec\\OrbbecSDK 2.4.3\\bin"
        - "D:\\openni22\\0013e-main\\OpenNI_2.3.0.66\\Windows\\SDK\\x64\\Redist"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\DTS\\Binn\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\pycharm\\PyCharm 2025.1.2\\bin"
        - "D:\\cursor\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:\\vscode\\Microsoft VS Code\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:25 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/"
    found: "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "D:\\opennier\\OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)\\Win64-Release\\sdk\\libs"
        - "D:\\python3.10\\Scripts\\"
        - "D:\\python3.10\\"
        - "D:\\orbbec\\OrbbecSDK 2.4.3\\bin"
        - "D:\\openni22\\0013e-main\\OpenNI_2.3.0.66\\Windows\\SDK\\x64\\Redist"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\DTS\\Binn\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\pycharm\\PyCharm 2025.1.2\\bin"
        - "D:\\cursor\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:\\vscode\\Microsoft VS Code\\bin"
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:25 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
      鐢熸垚鍚姩鏃堕棿涓?2025/8/5 14:19:23銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\4.1.0-rc3\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\4.1.0-rc3\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淒:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\4.1.0-rc3\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.53
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/workfile/lanhai-driver-branch_noweb/build/CMakeFiles/4.1.0-rc3/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:65 (__resolve_tool_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:104 (__resolve_linker_path)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:25 (project)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lld-link"
    candidate_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/"
      - "D:/python3.10/Scripts/"
      - "D:/python3.10/"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/pycharm/PyCharm 2025.1.2/bin/"
      - "D:/cursor/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/vscode/Microsoft VS Code/bin/"
    searched_directories:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.com"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link.exe"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lld-link"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/lld-link.com"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/lld-link.exe"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/lld-link"
      - "D:/python3.10/Scripts/lld-link.com"
      - "D:/python3.10/Scripts/lld-link.exe"
      - "D:/python3.10/Scripts/lld-link"
      - "D:/python3.10/lld-link.com"
      - "D:/python3.10/lld-link.exe"
      - "D:/python3.10/lld-link"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/lld-link.com"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/lld-link.exe"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/lld-link"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/lld-link.com"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/lld-link.exe"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/lld-link"
      - "C:/Windows/System32/lld-link.com"
      - "C:/Windows/System32/lld-link.exe"
      - "C:/Windows/System32/lld-link"
      - "C:/Windows/lld-link.com"
      - "C:/Windows/lld-link.exe"
      - "C:/Windows/lld-link"
      - "C:/Windows/System32/wbem/lld-link.com"
      - "C:/Windows/System32/wbem/lld-link.exe"
      - "C:/Windows/System32/wbem/lld-link"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/lld-link"
      - "C:/Windows/System32/OpenSSH/lld-link.com"
      - "C:/Windows/System32/OpenSSH/lld-link.exe"
      - "C:/Windows/System32/OpenSSH/lld-link"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/lld-link"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/lld-link"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/lld-link"
      - "C:/Program Files/dotnet/lld-link.com"
      - "C:/Program Files/dotnet/lld-link.exe"
      - "C:/Program Files/dotnet/lld-link"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/lld-link"
      - "C:/Program Files/CMake/bin/lld-link.com"
      - "C:/Program Files/CMake/bin/lld-link.exe"
      - "C:/Program Files/CMake/bin/lld-link"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/lld-link.com"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/lld-link.exe"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/lld-link"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/lld-link"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/lld-link.com"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/lld-link.exe"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/lld-link"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/lld-link"
      - "D:/pycharm/PyCharm 2025.1.2/bin/lld-link.com"
      - "D:/pycharm/PyCharm 2025.1.2/bin/lld-link.exe"
      - "D:/pycharm/PyCharm 2025.1.2/bin/lld-link"
      - "D:/cursor/cursor/resources/app/bin/lld-link.com"
      - "D:/cursor/cursor/resources/app/bin/lld-link.exe"
      - "D:/cursor/cursor/resources/app/bin/lld-link"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.com"
      - "C:/Users/<USER>/.dotnet/tools/lld-link.exe"
      - "C:/Users/<USER>/.dotnet/tools/lld-link"
      - "D:/vscode/Microsoft VS Code/bin/lld-link.com"
      - "D:/vscode/Microsoft VS Code/bin/lld-link.exe"
      - "D:/vscode/Microsoft VS Code/bin/lld-link"
    found: false
    search_context:
      ENV{PATH}:
        - "D:\\opennier\\OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)\\Win64-Release\\sdk\\libs"
        - "D:\\python3.10\\Scripts\\"
        - "D:\\python3.10\\"
        - "D:\\orbbec\\OrbbecSDK 2.4.3\\bin"
        - "D:\\openni22\\0013e-main\\OpenNI_2.3.0.66\\Windows\\SDK\\x64\\Redist"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\DTS\\Binn\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\pycharm\\PyCharm 2025.1.2\\bin"
        - "D:\\cursor\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:\\vscode\\Microsoft VS Code\\bin"
  -
    kind: "find-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:573 (enable_language)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC.cmake:546 (__windows_compiler_msvc_enable_rc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-MSVC-C.cmake:5 (__windows_compiler_msvc)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCInformation.cmake:48 (include)"
      - "CMakeLists.txt:25 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "rc"
    candidate_directories:
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/"
      - "D:/python3.10/Scripts/"
      - "D:/python3.10/"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/"
      - "C:/Program Files/dotnet/"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "D:/pycharm/PyCharm 2025.1.2/bin/"
      - "D:/cursor/cursor/resources/app/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/vscode/Microsoft VS Code/bin/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files/CMake/bin/"
      - "C:/Program Files/CMake/sbin/"
      - "C:/Program Files/CMake/"
      - "C:/Program Files (x86)/bluesea/bin/"
      - "C:/Program Files (x86)/bluesea/sbin/"
      - "C:/Program Files (x86)/bluesea/"
    searched_directories:
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/rc.com"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/rc.exe"
      - "D:/opennier/OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)/Win64-Release/sdk/libs/rc"
      - "D:/python3.10/Scripts/rc.com"
      - "D:/python3.10/Scripts/rc.exe"
      - "D:/python3.10/Scripts/rc"
      - "D:/python3.10/rc.com"
      - "D:/python3.10/rc.exe"
      - "D:/python3.10/rc"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/rc.com"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/rc.exe"
      - "D:/orbbec/OrbbecSDK 2.4.3/bin/rc"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/rc.com"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/rc.exe"
      - "D:/openni22/0013e-main/OpenNI_2.3.0.66/Windows/SDK/x64/Redist/rc"
      - "C:/Windows/System32/rc.com"
      - "C:/Windows/System32/rc.exe"
      - "C:/Windows/System32/rc"
      - "C:/Windows/rc.com"
      - "C:/Windows/rc.exe"
      - "C:/Windows/rc"
      - "C:/Windows/System32/wbem/rc.com"
      - "C:/Windows/System32/wbem/rc.exe"
      - "C:/Windows/System32/wbem/rc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/rc"
      - "C:/Windows/System32/OpenSSH/rc.com"
      - "C:/Windows/System32/OpenSSH/rc.exe"
      - "C:/Windows/System32/OpenSSH/rc"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/rc.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/rc.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/rc"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/rc.com"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/rc.exe"
      - "C:/Program Files/Microsoft SQL Server/150/Tools/Binn/rc"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/rc.com"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/rc.exe"
      - "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/170/Tools/Binn/rc"
      - "C:/Program Files/dotnet/rc.com"
      - "C:/Program Files/dotnet/rc.exe"
      - "C:/Program Files/dotnet/rc"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc.com"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc.exe"
      - "C:/Program Files (x86)/Windows Kits/10/Windows Performance Toolkit/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/rc.com"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/rc.exe"
      - "C:/Program Files (x86)/Microsoft SQL Server/110/Tools/Binn/rc"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/rc.com"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/rc.exe"
      - "C:/Program Files/Microsoft SQL Server/110/Tools/Binn/rc"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/rc.com"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/rc.exe"
      - "C:/Program Files/Microsoft SQL Server/110/DTS/Binn/rc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/rc"
      - "D:/pycharm/PyCharm 2025.1.2/bin/rc.com"
      - "D:/pycharm/PyCharm 2025.1.2/bin/rc.exe"
      - "D:/pycharm/PyCharm 2025.1.2/bin/rc"
      - "D:/cursor/cursor/resources/app/bin/rc.com"
      - "D:/cursor/cursor/resources/app/bin/rc.exe"
      - "D:/cursor/cursor/resources/app/bin/rc"
      - "C:/Users/<USER>/.dotnet/tools/rc.com"
      - "C:/Users/<USER>/.dotnet/tools/rc.exe"
      - "C:/Users/<USER>/.dotnet/tools/rc"
      - "D:/vscode/Microsoft VS Code/bin/rc.com"
      - "D:/vscode/Microsoft VS Code/bin/rc.exe"
      - "D:/vscode/Microsoft VS Code/bin/rc"
      - "C:/Program Files/bin/rc.com"
      - "C:/Program Files/bin/rc.exe"
      - "C:/Program Files/bin/rc"
      - "C:/Program Files/sbin/rc.com"
      - "C:/Program Files/sbin/rc.exe"
      - "C:/Program Files/sbin/rc"
      - "C:/Program Files/rc.com"
      - "C:/Program Files/rc.exe"
      - "C:/Program Files/rc"
      - "C:/Program Files (x86)/bin/rc.com"
      - "C:/Program Files (x86)/bin/rc.exe"
      - "C:/Program Files (x86)/bin/rc"
      - "C:/Program Files (x86)/sbin/rc.com"
      - "C:/Program Files (x86)/sbin/rc.exe"
      - "C:/Program Files (x86)/sbin/rc"
      - "C:/Program Files (x86)/rc.com"
      - "C:/Program Files (x86)/rc.exe"
      - "C:/Program Files (x86)/rc"
      - "C:/Program Files/CMake/bin/rc.com"
      - "C:/Program Files/CMake/bin/rc.exe"
      - "C:/Program Files/CMake/bin/rc"
      - "C:/Program Files/CMake/sbin/rc.com"
      - "C:/Program Files/CMake/sbin/rc.exe"
      - "C:/Program Files/CMake/sbin/rc"
      - "C:/Program Files/CMake/rc.com"
      - "C:/Program Files/CMake/rc.exe"
      - "C:/Program Files/CMake/rc"
      - "C:/Program Files (x86)/bluesea/bin/rc.com"
      - "C:/Program Files (x86)/bluesea/bin/rc.exe"
      - "C:/Program Files (x86)/bluesea/bin/rc"
      - "C:/Program Files (x86)/bluesea/sbin/rc.com"
      - "C:/Program Files (x86)/bluesea/sbin/rc.exe"
      - "C:/Program Files (x86)/bluesea/sbin/rc"
      - "C:/Program Files (x86)/bluesea/rc.com"
      - "C:/Program Files (x86)/bluesea/rc.exe"
      - "C:/Program Files (x86)/bluesea/rc"
    found: false
    search_context:
      ENV{PATH}:
        - "D:\\opennier\\OpenNI_v2.3.0.85_20220615_1b09bbfd_windows_x64_x86_release (1)\\Win64-Release\\sdk\\libs"
        - "D:\\python3.10\\Scripts\\"
        - "D:\\python3.10\\"
        - "D:\\orbbec\\OrbbecSDK 2.4.3\\bin"
        - "D:\\openni22\\0013e-main\\OpenNI_2.3.0.66\\Windows\\SDK\\x64\\Redist"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\"
        - "C:\\Program Files\\CMake\\bin"
        - "C:\\Program Files (x86)\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\Tools\\Binn\\"
        - "C:\\Program Files\\Microsoft SQL Server\\110\\DTS\\Binn\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\pycharm\\PyCharm 2025.1.2\\bin"
        - "D:\\cursor\\cursor\\resources\\app\\bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:\\vscode\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/bluesea"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/Program Files/CMake"
        - "C:/Program Files (x86)/bluesea"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:25 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/workfile/lanhai-driver-branch_noweb/build/CMakeFiles/CMakeScratch/TryCompile-m9a5wh"
      binary: "D:/workfile/lanhai-driver-branch_noweb/build/CMakeFiles/CMakeScratch/TryCompile-m9a5wh"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/workfile/lanhai-driver-branch_noweb/build/CMakeFiles/CMakeScratch/TryCompile-m9a5wh'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_efd4f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/8/5 14:19:24銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m9a5wh\\cmTC_efd4f.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_efd4f.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m9a5wh\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_efd4f.dir\\Debug\\cmTC_efd4f.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_efd4f.dir\\Debug\\cmTC_efd4f.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_efd4f.dir\\Debug\\cmTC_efd4f.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_efd4f.dir\\Debug\\\\" /Fd"cmTC_efd4f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Fo"cmTC_efd4f.dir\\Debug\\\\" /Fd"cmTC_efd4f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m9a5wh\\Debug\\cmTC_efd4f.exe" /INCREMENTAL /ILK:"cmTC_efd4f.dir\\Debug\\cmTC_efd4f.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/workfile/lanhai-driver-branch_noweb/build/CMakeFiles/CMakeScratch/TryCompile-m9a5wh/Debug/cmTC_efd4f.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/workfile/lanhai-driver-branch_noweb/build/CMakeFiles/CMakeScratch/TryCompile-m9a5wh/Debug/cmTC_efd4f.lib" /MACHINE:X64  /machine:x64 cmTC_efd4f.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_efd4f.vcxproj -> D:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m9a5wh\\Debug\\cmTC_efd4f.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_efd4f.dir\\Debug\\cmTC_efd4f.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_efd4f.dir\\Debug\\cmTC_efd4f.tlog\\cmTC_efd4f.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\CMakeScratch\\TryCompile-m9a5wh\\cmTC_efd4f.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.46
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:25 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:25 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35211.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:25 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/workfile/lanhai-driver-branch_noweb/build/CMakeFiles/CMakeScratch/TryCompile-o3pcae"
      binary: "D:/workfile/lanhai-driver-branch_noweb/build/CMakeFiles/CMakeScratch/TryCompile-o3pcae"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/workfile/lanhai-driver-branch_noweb/build/CMakeFiles/CMakeScratch/TryCompile-o3pcae'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_b2e79.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/8/5 14:19:25銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o3pcae\\cmTC_b2e79.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_b2e79.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o3pcae\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_b2e79.dir\\Debug\\cmTC_b2e79.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_b2e79.dir\\Debug\\cmTC_b2e79.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_b2e79.dir\\Debug\\cmTC_b2e79.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_b2e79.dir\\Debug\\\\" /Fd"cmTC_b2e79.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /GR /Fo"cmTC_b2e79.dir\\Debug\\\\" /Fd"cmTC_b2e79.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /OUT:"D:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o3pcae\\Debug\\cmTC_b2e79.exe" /INCREMENTAL /ILK:"cmTC_b2e79.dir\\Debug\\cmTC_b2e79.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/workfile/lanhai-driver-branch_noweb/build/CMakeFiles/CMakeScratch/TryCompile-o3pcae/Debug/cmTC_b2e79.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /IMPLIB:"D:/workfile/lanhai-driver-branch_noweb/build/CMakeFiles/CMakeScratch/TryCompile-o3pcae/Debug/cmTC_b2e79.lib" /MACHINE:X64  /machine:x64 cmTC_b2e79.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_b2e79.vcxproj -> D:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o3pcae\\Debug\\cmTC_b2e79.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_b2e79.dir\\Debug\\cmTC_b2e79.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_b2e79.dir\\Debug\\cmTC_b2e79.tlog\\cmTC_b2e79.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\workfile\\lanhai-driver-branch_noweb\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o3pcae\\cmTC_b2e79.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.47
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:25 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:25 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35211.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
